const express = require("express");
const cors = require("cors");
const cookieParser = require("cookie-parser");
const prismaService = require("./lib/prisma");

// Import routes
const authRoutes = require("./routes/auth");
const eventRoutes = require("./routes/events");
const cartRoutes = require("./routes/cart");
const orderRoutes = require("./routes/orders");
const interestedRoutes = require("./routes/interested");
const ticketRoutes = require("./routes/tickets");

const app = express();

// Middleware
app.use(
  cors({
    origin: ["http://localhost:3000", "http://localhost:5000"],
    methods: ["GET", "POST", "PUT", "DELETE"],
    credentials: true,
    exposedHeaders: ["Content-Length", "Content-Type"],
  })
);
app.use(cookieParser());
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/events", eventRoutes);
app.use("/api/cart", cartRoutes);
app.use("/api/orders", orderRoutes);
app.use("/api/interested", interestedRoutes);
app.use("/api/tickets", ticketRoutes);

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({
    success: true,
    message: "Server is running",
    timestamp: new Date().toISOString(),
  });
});

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found",
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error("Server error:", error);
  res.status(500).json({
    success: false,
    message: "Internal server error",
  });
});

const port = process.env.PORT || 5000;

// Initialize database connection and start server
async function startServer() {
  try {
    await prismaService.connect();

    app.listen(port, () => {
      console.log(`Server is running on port ${port}`);
      console.log(`Environment: ${process.env.NODE_ENV || "development"}`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
  console.log("Shutting down server...");
  await prismaService.disconnect();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("Shutting down server...");
  await prismaService.disconnect();
  process.exit(0);
});

startServer();
