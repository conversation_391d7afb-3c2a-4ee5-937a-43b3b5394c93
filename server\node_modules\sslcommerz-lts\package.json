{"name": "sslcommerz-lts", "description": "", "version": "1.1.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/sslcommerz/SSLCommerz-NodeJS.git"}, "keywords": [], "author": "SSLCommerz Integration Team", "license": "ISC", "bugs": {"email": "<EMAIL>"}, "homepage": "https://github.com/sslcommerz/SSLCommerz-NodeJS.git#readme", "dependencies": {"form-data": "2.5.0", "node-fetch": "2.6.1"}}