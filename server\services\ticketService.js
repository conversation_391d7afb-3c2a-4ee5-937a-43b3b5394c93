const { PrismaClient } = require("@prisma/client");
const { createClient } = require("@supabase/supabase-js");
const { PDFDocument, rgb, StandardFonts } = require("pdf-lib");
const QRCode = require("qrcode");
const { v4: uuidv4 } = require("uuid");

const prisma = new PrismaClient();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

class TicketService {
  static instance = null;

  static getInstance() {
    if (!TicketService.instance) {
      TicketService.instance = new TicketService();
    }
    return TicketService.instance;
  }

  /**
   * Generate a unique 12-character URL-encoded QR code
   * Contains eventId, ticketId, ticketTypeId encoded in a compact format
   */
  generateQRCode(eventId, ticketId, ticketTypeId) {
    // Create a compact string with event, ticket, and type IDs
    const dataString = `${eventId}-${ticketId}-${ticketTypeId}`;

    // Create a hash-like string using base36 encoding for compactness
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);

    // Combine and ensure it's exactly 12 characters
    let qrCode = (timestamp + random).substring(0, 12).toUpperCase();

    // Ensure uniqueness by adding event/ticket info if needed
    if (qrCode.length < 12) {
      const padding = (eventId.toString() + ticketId.toString()).substring(
        0,
        12 - qrCode.length
      );
      qrCode += padding;
    }

    // URL encode for safe transmission
    return encodeURIComponent(qrCode);
  }

  /**
   * Create tickets for an order with attendee information
   */
  async createTicketsForOrder(orderData, ticketsWithAttendeeInfo) {
    try {
      const createdTickets = [];

      for (const ticketInfo of ticketsWithAttendeeInfo) {
        const { ticketTypeId, attendeeInfo } = ticketInfo;

        // Create the ticket record
        const ticket = await prisma.tickets.create({
          data: {
            order_id: orderData.order_id,
            ticket_type_id: ticketTypeId,
            qr_code: "", // Will be updated after we have the ticket_id
            attendee_name: attendeeInfo.name,
            attendee_email: attendeeInfo.email,
            attendee_phone: attendeeInfo.phone,
            is_validated: false,
            user_ticketpdf: null, // Will be updated after PDF generation
          },
        });

        // Generate QR code with the actual ticket ID
        const qrCode = this.generateQRCode(
          orderData.eventId,
          ticket.ticket_id,
          ticketTypeId
        );

        // Update ticket with QR code
        const updatedTicket = await prisma.tickets.update({
          where: { ticket_id: ticket.ticket_id },
          data: { qr_code: qrCode },
          include: {
            tickettypes: {
              include: {
                events: true,
              },
            },
          },
        });

        createdTickets.push(updatedTicket);
      }

      return createdTickets;
    } catch (error) {
      console.error("Error creating tickets:", error);
      throw new Error("Failed to create tickets");
    }
  }

  /**
   * Generate PDF ticket using template and ticket data
   */
  async generateTicketPDF(ticket) {
    try {
      const event = ticket.tickettypes.events;

      // Get PDF template from Supabase storage
      const templatePath =
        ticket.tickettypes.pdf_template ||
        "ticket-type-pdf-template/Universal Template.pdf";
      const { data: templateData, error: downloadError } =
        await supabase.storage.from("assets").download(templatePath);

      if (downloadError) {
        console.error("Error downloading PDF template:", downloadError);
        throw new Error("Failed to download PDF template");
      }

      // Convert blob to array buffer
      const templateBuffer = await templateData.arrayBuffer();

      // Load the PDF template
      const pdfDoc = await PDFDocument.load(templateBuffer);
      const pages = pdfDoc.getPages();
      const firstPage = pages[0];

      // Get page dimensions
      const { width, height } = firstPage.getSize();

      // Embed font
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
      const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

      // Generate QR code image
      const qrCodeDataURL = await QRCode.toDataURL(ticket.qr_code, {
        width: 150,
        margin: 1,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      // Convert data URL to buffer
      const qrCodeBuffer = Buffer.from(qrCodeDataURL.split(",")[1], "base64");
      const qrCodeImage = await pdfDoc.embedPng(qrCodeBuffer);

      // Add dynamic content to PDF
      firstPage.drawText(event.title, {
        x: 50,
        y: height - 100,
        size: 24,
        font: boldFont,
        color: rgb(0, 0, 0),
      });

      firstPage.drawText(`Ticket Type: ${ticket.tickettypes.name}`, {
        x: 50,
        y: height - 140,
        size: 14,
        font: font,
        color: rgb(0, 0, 0),
      });

      firstPage.drawText(`Attendee: ${ticket.attendee_name}`, {
        x: 50,
        y: height - 170,
        size: 12,
        font: font,
        color: rgb(0, 0, 0),
      });

      firstPage.drawText(`Email: ${ticket.attendee_email}`, {
        x: 50,
        y: height - 190,
        size: 10,
        font: font,
        color: rgb(0, 0, 0),
      });

      firstPage.drawText(`Phone: ${ticket.attendee_phone}`, {
        x: 50,
        y: height - 210,
        size: 10,
        font: font,
        color: rgb(0, 0, 0),
      });

      // Add event policy if available
      if (event.event_policy) {
        firstPage.drawText("Terms & Conditions:", {
          x: 50,
          y: height - 250,
          size: 12,
          font: boldFont,
          color: rgb(0, 0, 0),
        });

        // Split policy text into lines to fit on page
        const policyLines = this.splitTextIntoLines(event.event_policy, 60);
        policyLines.slice(0, 5).forEach((line, index) => {
          firstPage.drawText(line, {
            x: 50,
            y: height - 270 - index * 15,
            size: 9,
            font: font,
            color: rgb(0, 0, 0),
          });
        });
      }

      // Add QR code
      firstPage.drawImage(qrCodeImage, {
        x: width - 200,
        y: height - 250,
        width: 150,
        height: 150,
      });

      // Add QR code text
      firstPage.drawText(ticket.qr_code, {
        x: width - 200,
        y: height - 270,
        size: 10,
        font: font,
        color: rgb(0, 0, 0),
      });

      // Serialize PDF
      const pdfBytes = await pdfDoc.save();

      return pdfBytes;
    } catch (error) {
      console.error("Error generating PDF:", error);
      throw new Error("Failed to generate PDF ticket");
    }
  }

  /**
   * Upload PDF to Supabase storage and return the file path
   */
  async uploadPDFToStorage(pdfBytes, ticket) {
    try {
      // Generate unique filename
      const fileName = `ticket-${ticket.ticket_id}-${ticket.qr_code}.pdf`;
      const filePath = `ticket-pdfs/${fileName}`;

      // Upload to Supabase storage
      const { error } = await supabase.storage
        .from("assets")
        .upload(filePath, pdfBytes, {
          contentType: "application/pdf",
          upsert: true,
        });

      if (error) {
        console.error("Error uploading PDF:", error);
        throw new Error("Failed to upload PDF to storage");
      }

      return filePath;
    } catch (error) {
      console.error("Error in uploadPDFToStorage:", error);
      throw error;
    }
  }

  /**
   * Complete ticket creation workflow: create tickets, generate PDFs, upload to storage
   */
  async createCompleteTickets(orderData, ticketsWithAttendeeInfo) {
    try {
      // Step 1: Create ticket records
      const tickets = await this.createTicketsForOrder(
        orderData,
        ticketsWithAttendeeInfo
      );

      // Step 2: Generate PDFs and upload to storage for each ticket
      const updatedTickets = [];

      for (const ticket of tickets) {
        try {
          // Generate PDF
          const pdfBytes = await this.generateTicketPDF(ticket);

          // Upload to storage
          const pdfPath = await this.uploadPDFToStorage(pdfBytes, ticket);

          // Update ticket with PDF path
          const updatedTicket = await prisma.tickets.update({
            where: { ticket_id: ticket.ticket_id },
            data: { user_ticketpdf: pdfPath },
            include: {
              tickettypes: {
                include: {
                  events: true,
                },
              },
            },
          });

          updatedTickets.push(updatedTicket);
        } catch (pdfError) {
          console.error(
            `Error processing PDF for ticket ${ticket.ticket_id}:`,
            pdfError
          );
          // Continue with other tickets even if one fails
          updatedTickets.push(ticket);
        }
      }

      return updatedTickets;
    } catch (error) {
      console.error("Error in createCompleteTickets:", error);
      throw error;
    }
  }

  /**
   * Get PDF file from storage for download
   */
  async getTicketPDF(ticketId, userId) {
    try {
      // Verify ticket belongs to user
      const ticket = await prisma.tickets.findFirst({
        where: {
          ticket_id: ticketId,
          orders: {
            user_id: userId,
            payment_status: "completed",
          },
        },
      });

      if (!ticket || !ticket.user_ticketpdf) {
        throw new Error("Ticket not found or PDF not available");
      }

      // Download from Supabase storage
      const { data, error } = await supabase.storage
        .from("assets")
        .download(ticket.user_ticketpdf);

      if (error) {
        console.error("Error downloading PDF:", error);
        throw new Error("Failed to download PDF");
      }

      return {
        data,
        filename: `ticket-${ticketId}.pdf`,
        contentType: "application/pdf",
      };
    } catch (error) {
      console.error("Error getting ticket PDF:", error);
      throw error;
    }
  }

  /**
   * Create order and order items for ticket purchase
   */
  async createOrderWithItems(userId, selectedTickets, eventId) {
    const transId = uuidv4();

    try {
      // Calculate total amount
      const totalAmount = selectedTickets.reduce((sum, ticket) => {
        return sum + parseFloat(ticket.price) * ticket.quantity;
      }, 0);

      // Create order
      const order = await prisma.orders.create({
        data: {
          user_id: userId,
          total_amount: totalAmount,
          additional_fees: 0.0,
          payment_status: "pending", // For now, assuming immediate completion
          payment_method: null,
          transaction_id: `${transId}_${userId}`,
        },
      });

      // Create order items
      for (const ticket of selectedTickets) {
        await prisma.orderitems.create({
          data: {
            order_id: order.order_id,
            ticket_type_id: ticket.ticketTypeId,
            quantity: ticket.quantity,
            unit_price: parseFloat(ticket.price),
          },
        });
      }

      return { ...order, eventId };
    } catch (error) {
      console.error("Error creating order:", error);
      throw new Error("Failed to create order");
    }
  }

  /**
   * Utility function to split text into lines
   */
  splitTextIntoLines(text, maxCharsPerLine) {
    const words = text.split(" ");
    const lines = [];
    let currentLine = "";

    for (const word of words) {
      if ((currentLine + word).length <= maxCharsPerLine) {
        currentLine += (currentLine ? " " : "") + word;
      } else {
        if (currentLine) lines.push(currentLine);
        currentLine = word;
      }
    }

    if (currentLine) lines.push(currentLine);
    return lines;
  }
}

module.exports = TicketService;
