const { PrismaClient } = require("@prisma/client");
const { createClient } = require("@supabase/supabase-js");
const QRCode = require("qrcode");
const { PDFDocument, rgb, StandardFonts } = require("pdf-lib");
const { v4: uuidv4 } = require("uuid");

const prisma = new PrismaClient();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

class TicketService {
  static instance = null;

  static getInstance() {
    if (!TicketService.instance) {
      TicketService.instance = new TicketService();
    }
    return TicketService.instance;
  }

  /**
   * Generate a unique 12-character URL-encoded QR code identifier
   * Format: eventid-ticketid-tickettype encoded
   */
  generateQRCode(eventId, ticketId, ticketTypeId) {
    try {
      // Create a unique identifier combining event, ticket, and type info
      const identifier = `${eventId}-${ticketId}-${ticketTypeId}-${Date.now()}`;

      // Generate a shorter unique code using UUID and encoding
      const uuid = uuidv4().replace(/-/g, "").substring(0, 8);
      const timestamp = Date.now().toString(36).substring(-4);

      // Combine to create 12-character code
      const qrCode = (uuid + timestamp).substring(0, 12).toUpperCase();

      return qrCode;
    } catch (error) {
      console.error("Error generating QR code:", error);
      throw new Error("Failed to generate QR code");
    }
  }

  /**
   * Generate QR code image as base64 string
   */
  async generateQRCodeImage(qrCodeData) {
    try {
      const qrCodeImage = await QRCode.toDataURL(qrCodeData, {
        errorCorrectionLevel: "M",
        type: "image/png",
        quality: 0.92,
        margin: 1,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
        width: 200,
      });

      return qrCodeImage;
    } catch (error) {
      console.error("Error generating QR code image:", error);
      throw new Error("Failed to generate QR code image");
    }
  }

  /**
   * Create PDF ticket using pdf-lib
   */
  async createTicketPDF(ticketData) {
    try {
      const {
        eventTitle,
        eventDate,
        eventTime,
        eventVenue,
        ticketType,
        ticketCategory,
        attendeeName,
        attendeeEmail,
        attendeePhone,
        qrCode,
        eventPolicy,
        price,
      } = ticketData;

      // Create a new PDF document
      const pdfDoc = await PDFDocument.create();
      const page = pdfDoc.addPage([600, 800]);

      // Embed fonts
      const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
      const helveticaBoldFont = await pdfDoc.embedFont(
        StandardFonts.HelveticaBold
      );

      // Generate QR code image
      const qrCodeImage = await this.generateQRCodeImage(qrCode);
      const qrCodeImageBytes = Buffer.from(qrCodeImage.split(",")[1], "base64");
      const qrCodePngImage = await pdfDoc.embedPng(qrCodeImageBytes);

      // Set up colors
      const primaryColor = rgb(0.8, 0.2, 0.2); // Red color
      const textColor = rgb(0.1, 0.1, 0.1);
      const grayColor = rgb(0.5, 0.5, 0.5);

      let yPosition = 750;

      // Header
      page.drawText("EVENT TICKET", {
        x: 50,
        y: yPosition,
        size: 24,
        font: helveticaBoldFont,
        color: primaryColor,
      });

      yPosition -= 40;

      // Event Title
      page.drawText(eventTitle, {
        x: 50,
        y: yPosition,
        size: 20,
        font: helveticaBoldFont,
        color: textColor,
      });

      yPosition -= 30;

      // Event Details
      page.drawText(`Date: ${new Date(eventDate).toLocaleDateString()}`, {
        x: 50,
        y: yPosition,
        size: 12,
        font: helveticaFont,
        color: textColor,
      });

      yPosition -= 20;

      page.drawText(`Time: ${eventTime}`, {
        x: 50,
        y: yPosition,
        size: 12,
        font: helveticaFont,
        color: textColor,
      });

      yPosition -= 20;

      page.drawText(`Venue: ${eventVenue}`, {
        x: 50,
        y: yPosition,
        size: 12,
        font: helveticaFont,
        color: textColor,
      });

      yPosition -= 40;

      // Ticket Information
      page.drawText("TICKET INFORMATION", {
        x: 50,
        y: yPosition,
        size: 16,
        font: helveticaBoldFont,
        color: primaryColor,
      });

      yPosition -= 25;

      page.drawText(`Category: ${ticketCategory}`, {
        x: 50,
        y: yPosition,
        size: 12,
        font: helveticaFont,
        color: textColor,
      });

      yPosition -= 20;

      page.drawText(`Type: ${ticketType}`, {
        x: 50,
        y: yPosition,
        size: 12,
        font: helveticaFont,
        color: textColor,
      });

      yPosition -= 20;

      page.drawText(`Price: $${price}`, {
        x: 50,
        y: yPosition,
        size: 12,
        font: helveticaFont,
        color: textColor,
      });

      yPosition -= 40;

      // Attendee Information
      page.drawText("ATTENDEE INFORMATION", {
        x: 50,
        y: yPosition,
        size: 16,
        font: helveticaBoldFont,
        color: primaryColor,
      });

      yPosition -= 25;

      page.drawText(`Name: ${attendeeName}`, {
        x: 50,
        y: yPosition,
        size: 12,
        font: helveticaFont,
        color: textColor,
      });

      yPosition -= 20;

      page.drawText(`Email: ${attendeeEmail}`, {
        x: 50,
        y: yPosition,
        size: 12,
        font: helveticaFont,
        color: textColor,
      });

      yPosition -= 20;

      page.drawText(`Phone: ${attendeePhone}`, {
        x: 50,
        y: yPosition,
        size: 12,
        font: helveticaFont,
        color: textColor,
      });

      // QR Code
      page.drawImage(qrCodePngImage, {
        x: 400,
        y: yPosition - 100,
        width: 150,
        height: 150,
      });

      page.drawText("Scan QR Code for Entry", {
        x: 420,
        y: yPosition - 120,
        size: 10,
        font: helveticaFont,
        color: grayColor,
      });

      page.drawText(`Code: ${qrCode}`, {
        x: 430,
        y: yPosition - 135,
        size: 10,
        font: helveticaFont,
        color: grayColor,
      });

      yPosition -= 180;

      // Terms and Conditions
      if (eventPolicy) {
        page.drawText("TERMS & CONDITIONS", {
          x: 50,
          y: yPosition,
          size: 14,
          font: helveticaBoldFont,
          color: primaryColor,
        });

        yPosition -= 20;

        // Split policy text into lines
        const policyLines = this.splitTextIntoLines(eventPolicy, 70);
        policyLines.forEach((line) => {
          if (yPosition > 50) {
            page.drawText(line, {
              x: 50,
              y: yPosition,
              size: 10,
              font: helveticaFont,
              color: textColor,
            });
            yPosition -= 15;
          }
        });
      }

      // Footer
      page.drawText(
        "This ticket is non-transferable and must be presented for entry.",
        {
          x: 50,
          y: 30,
          size: 8,
          font: helveticaFont,
          color: grayColor,
        }
      );

      // Serialize the PDF
      const pdfBytes = await pdfDoc.save();
      return pdfBytes;
    } catch (error) {
      console.error("Error creating PDF ticket:", error);
      throw new Error("Failed to create PDF ticket");
    }
  }

  /**
   * Helper method to split text into lines
   */
  splitTextIntoLines(text, maxCharsPerLine) {
    const words = text.split(" ");
    const lines = [];
    let currentLine = "";

    words.forEach((word) => {
      if ((currentLine + word).length <= maxCharsPerLine) {
        currentLine += (currentLine ? " " : "") + word;
      } else {
        if (currentLine) lines.push(currentLine);
        currentLine = word;
      }
    });

    if (currentLine) lines.push(currentLine);
    return lines;
  }

  /**
   * Upload PDF to Supabase storage
   */
  async uploadPDFToSupabase(pdfBytes, fileName) {
    try {
      const { data, error } = await supabase.storage
        .from("assets")
        .upload(`ticket-pdfs/${fileName}`, pdfBytes, {
          contentType: "application/pdf",
          upsert: false,
        });

      if (error) {
        console.error("Supabase upload error:", error);
        throw new Error(`Failed to upload PDF: ${error.message}`);
      }

      // Get the public URL
      const { data: urlData } = supabase.storage
        .from("assets")
        .getPublicUrl(`ticket-pdfs/${fileName}`);

      return {
        path: data.path,
        publicUrl: urlData.publicUrl,
      };
    } catch (error) {
      console.error("Error uploading PDF to Supabase:", error);
      throw new Error("Failed to upload PDF to storage");
    }
  }

  /**
   * Create a complete ticket with PDF generation and storage
   */
  async createTicket(ticketData) {
    try {
      const {
        orderId,
        ticketTypeId,
        attendeeName,
        attendeeEmail,
        attendeePhone,
        eventData,
        ticketTypeData,
        categoryData,
      } = ticketData;

      // Generate unique QR code
      const qrCode = this.generateQRCode(
        eventData.event_id,
        orderId,
        ticketTypeId
      );

      // Create ticket record in database first
      const ticket = await prisma.tickets.create({
        data: {
          order_id: orderId,
          ticket_type_id: ticketTypeId,
          qr_code: qrCode,
          attendee_name: attendeeName,
          attendee_email: attendeeEmail,
          attendee_phone: attendeePhone,
          is_validated: false,
        },
      });

      // Prepare PDF data
      const pdfData = {
        eventTitle: eventData.title,
        eventDate: eventData.start_date,
        eventTime: eventData.start_time,
        eventVenue: eventData.venue_name || "TBD",
        ticketType: ticketTypeData.name,
        ticketCategory: categoryData?.name || "General",
        attendeeName,
        attendeeEmail,
        attendeePhone,
        qrCode,
        eventPolicy: eventData.event_policy,
        price: parseFloat(ticketTypeData.price),
      };

      // Generate PDF
      const pdfBytes = await this.createTicketPDF(pdfData);

      // Generate unique filename
      const fileName = `ticket-${ticket.ticket_id}-${qrCode}.pdf`;

      // Upload PDF to Supabase
      const uploadResult = await this.uploadPDFToSupabase(pdfBytes, fileName);

      // Update ticket record with PDF path
      const updatedTicket = await prisma.tickets.update({
        where: { ticket_id: ticket.ticket_id },
        data: { user_ticketpdf: uploadResult.path },
      });

      return {
        ticket: updatedTicket,
        pdfUrl: uploadResult.publicUrl,
        qrCode,
      };
    } catch (error) {
      console.error("Error creating ticket:", error);
      throw new Error("Failed to create ticket");
    }
  }

  /**
   * Get ticket PDF download URL
   */
  async getTicketPDFUrl(ticketId) {
    try {
      const ticket = await prisma.tickets.findUnique({
        where: { ticket_id: ticketId },
        select: { user_ticketpdf: true },
      });

      if (!ticket || !ticket.user_ticketpdf) {
        throw new Error("Ticket PDF not found");
      }

      const { data } = supabase.storage
        .from("assets")
        .getPublicUrl(ticket.user_ticketpdf);

      return data.publicUrl;
    } catch (error) {
      console.error("Error getting ticket PDF URL:", error);
      throw new Error("Failed to get ticket PDF URL");
    }
  }

  /**
   * Download ticket PDF buffer
   */
  async downloadTicketPDF(ticketId) {
    try {
      const ticket = await prisma.tickets.findUnique({
        where: { ticket_id: ticketId },
        select: { user_ticketpdf: true },
      });

      if (!ticket || !ticket.user_ticketpdf) {
        throw new Error("Ticket PDF not found");
      }

      const { data, error } = await supabase.storage
        .from("assets")
        .download(ticket.user_ticketpdf);

      if (error) {
        throw new Error(`Failed to download PDF: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error downloading ticket PDF:", error);
      throw new Error("Failed to download ticket PDF");
    }
  }
}

module.exports = TicketService;
