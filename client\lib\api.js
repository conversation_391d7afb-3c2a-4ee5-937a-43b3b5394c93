import axios from "axios";

// Create axios instance with default config
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api",
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor - no need to add auth token as we use HTTP-only cookies
api.interceptors.request.use(
  (config) => {
    // Cookies are automatically sent with requests due to withCredentials: true
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle session validation
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Avoid infinite loop by not retrying validate-session endpoint
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !originalRequest.url?.includes("/auth/validate-session")
    ) {
      originalRequest._retry = true;

      try {
        // Try to validate session - create a new request without interceptors to avoid infinite loop
        const validateResponse = await axios.get(
          `${
            process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api"
          }/auth/validate-session`,
          {
            withCredentials: true,
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (validateResponse.data.success) {
          // Session is still valid, retry original request
          return api(originalRequest);
        } else {
          throw new Error("Session invalid");
        }
      } catch (sessionError) {
        // Session invalid, clear storage and redirect to login
        // Only redirect if we're not already on the home page to avoid infinite redirects
        localStorage.removeItem("user");
        if (typeof window !== "undefined" && window.location.pathname !== "/") {
          window.location.href = "/";
        }
        return Promise.reject(sessionError);
      }
    }

    // For validate-session endpoint failures, just clear storage (no redirect needed as this is expected)
    if (
      error.response?.status === 401 &&
      originalRequest.url?.includes("/auth/validate-session")
    ) {
      localStorage.removeItem("user");
      // Don't redirect here as 401 on validate-session is expected when no valid session exists
    }

    return Promise.reject(error);
  }
);

// Auth API functions
export const authAPI = {
  // Register
  register: async (userData) => {
    const response = await api.post("/auth/register", userData);
    return response.data;
  },

  // Login
  login: async (credentials) => {
    const response = await api.post("/auth/login", credentials);
    return response.data;
  },

  // Logout
  logout: async () => {
    const response = await api.post("/auth/logout");
    return response.data;
  },

  // Get current user
  getCurrentUser: async () => {
    const response = await api.get("/auth/me");
    return response.data;
  },

  // Verify email
  verifyEmail: async (token) => {
    const response = await api.get(`/auth/verify-email?token=${token}`);
    return response.data;
  },

  // Resend verification email
  resendVerificationEmail: async (email) => {
    const response = await api.post("/auth/resend-verification", { email });
    return response.data;
  },

  // Forgot password
  forgotPassword: async (email) => {
    const response = await api.post("/auth/forgot-password", { email });
    return response.data;
  },

  // Reset password
  resetPassword: async (token, newPassword) => {
    const response = await api.post("/auth/reset-password", {
      token,
      newPassword,
    });
    return response.data;
  },

  // Change password
  changePassword: async (currentPassword, newPassword) => {
    const response = await api.post("/auth/change-password", {
      currentPassword,
      newPassword,
    });
    return response.data;
  },

  // Update profile
  updateProfile: async (profileData) => {
    const response = await api.put("/auth/update-profile", profileData);
    return response.data;
  },

  // Get OAuth URL
  getOAuthUrl: async (provider) => {
    const response = await api.get(`/auth/oauth/${provider}`);
    return response.data;
  },

  // Sync OAuth user data
  syncOAuthUser: async (supabaseUserData) => {
    const response = await api.post("/auth/oauth/sync", { supabaseUserData });
    return response.data;
  },

  // Validate session - use direct axios call to avoid interceptor infinite loop
  validateSession: async () => {
    try {
      const response = await axios.get(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api"
        }/auth/validate-session`,
        {
          withCredentials: true,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    } catch (error) {
      // Handle 401 errors gracefully - this is expected when no valid session exists
      if (error.response?.status === 401) {
        return {
          success: false,
          message: "No valid session",
          error: "UNAUTHORIZED",
        };
      }
      // Re-throw other errors
      throw error;
    }
  },
};

// Generic API functions
export const apiRequest = {
  get: async (url, config = {}) => {
    const response = await api.get(url, config);
    return response.data;
  },

  post: async (url, data = {}, config = {}) => {
    const response = await api.post(url, data, config);
    return response.data;
  },

  put: async (url, data = {}, config = {}) => {
    const response = await api.put(url, data, config);
    return response.data;
  },

  delete: async (url, config = {}) => {
    const response = await api.delete(url, config);
    return response.data;
  },
};

// Events API functions (simplified for frontend filtering)
export const eventsAPI = {
  // Get all events (no server-side filtering)
  getAllEvents: async () => {
    const response = await api.get("/events");
    return response.data;
  },

  // Get event by ID
  getEventById: async (id) => {
    const response = await api.get(`/events/${id}`);
    return response.data;
  },

  // Get all genres
  getAllGenres: async () => {
    const response = await api.get("/events/genres");
    return response.data;
  },

  // Get all locations
  getAllLocations: async () => {
    const response = await api.get("/events/locations");
    return response.data;
  },

  // Get events by organizer
  getEventsByOrganizer: async (organizerId) => {
    const response = await api.get(`/events/organizer/${organizerId}`);
    return response.data;
  },
};

// Cart API functions
export const cartAPI = {
  // Get user's cart items
  getCartItems: async () => {
    const response = await api.get("/cart");
    return response.data;
  },

  // Add item to cart
  addToCart: async (ticketTypeId, quantity = 1) => {
    const response = await api.post("/cart", { ticketTypeId, quantity });
    return response.data;
  },

  // Update cart item quantity
  updateCartItemQuantity: async (cartId, quantity) => {
    const response = await api.put(`/cart/${cartId}`, { quantity });
    return response.data;
  },

  // Remove item from cart
  removeFromCart: async (cartId) => {
    const response = await api.delete(`/cart/${cartId}`);
    return response.data;
  },

  // Clear entire cart
  clearCart: async () => {
    const response = await api.delete("/cart");
    return response.data;
  },

  // Get cart summary
  getCartSummary: async () => {
    const response = await api.get("/cart/summary");
    return response.data;
  },
};

// Orders API functions
export const ordersAPI = {
  // Get user's orders
  getUserOrders: async () => {
    const response = await api.get("/orders");
    return response.data;
  },

  // Get user's tickets (formatted for dashboard)
  getUserTickets: async () => {
    const response = await api.get("/orders/tickets");
    return response.data;
  },

  // Get specific order details
  getOrderById: async (orderId) => {
    const response = await api.get(`/orders/${orderId}`);
    return response.data;
  },

  // Get user order statistics
  getUserOrderStats: async () => {
    const response = await api.get("/orders/stats");
    return response.data;
  },
};

// Tickets API functions
export const ticketsAPI = {
  // Create tickets with complete workflow
  createTickets: async (selectedTickets, ticketsWithAttendeeInfo, eventId) => {
    const response = await api.post("/tickets/create", {
      selectedTickets,
      ticketsWithAttendeeInfo,
      eventId,
    });
    return response.data;
  },

  // Download ticket PDF
  downloadTicketPDF: async (ticketId) => {
    const response = await api.get(`/tickets/${ticketId}/pdf`, {
      responseType: "blob",
    });
    return response;
  },

  // Get ticket details by QR code
  getTicketByQRCode: async (qrCode) => {
    const response = await api.get(`/tickets/qr/${encodeURIComponent(qrCode)}`);
    return response.data;
  },

  // Validate/scan a ticket
  validateTicket: async (ticketId, organizerId = null) => {
    const response = await api.post(`/tickets/${ticketId}/validate`, {
      organizerId,
    });
    return response.data;
  },
};

// Interested API functions
export const interestedAPI = {
  // Get user's interested events
  getUserInterestedEvents: async () => {
    const response = await api.get("/interested");
    return response.data;
  },

  // Add event to interested list
  addToInterested: async (eventId) => {
    const response = await api.post("/interested", { eventId });
    return response.data;
  },

  // Remove event from interested list
  removeFromInterested: async (eventId) => {
    const response = await api.delete(`/interested/${eventId}`);
    return response.data;
  },

  // Check if event is in user's interested list
  checkInterestedStatus: async (eventId) => {
    const response = await api.get(`/interested/check/${eventId}`);
    return response.data;
  },
};

export default api;
